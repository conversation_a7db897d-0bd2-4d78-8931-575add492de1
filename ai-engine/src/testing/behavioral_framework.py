"""
Behavioral Testing Framework for ModPorter AI

This module provides comprehensive behavioral testing capabilities for verifying
that converted Bedrock add-ons maintain original mod functionality through
automated in-game testing scenarios.
"""

import logging
import json
from typing import Dict, Any, List, Optional, Union
import time
import datetime
from pathlib import Path

# Configure basic logging if not already configured by another module
# logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

class GameStateTracker:
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.current_game_state: Dict[str, Any] = {}
        self.state_history: List[Dict[str, Any]] = []
        self.logger.info("GameStateTracker initialized.")

    def update_state(self, new_state_variables: Dict[str, Any]):
        self.logger.debug(f"Updating game state with: {new_state_variables}")
        self.current_game_state.update(new_state_variables)
        self._record_state_history()
        self.logger.debug(f"Current game state: {self.current_game_state}")

    def _record_state_history(self):
        snapshot = json.loads(json.dumps(self.current_game_state))
        self.state_history.append(snapshot)
        self.logger.debug(f"Recorded state snapshot. History length: {len(self.state_history)}")

    def get_current_state(self, specific_keys: List[str] = None) -> Dict[str, Any]:
        if specific_keys:
            return {key: self.current_game_state.get(key) for key in specific_keys if key in self.current_game_state}
        return self.current_game_state.copy()

    def get_state_history(self) -> List[Dict[str, Any]]:
        return self.state_history

    def reset_state(self):
        self.logger.info("Resetting game state and history.")
        self.current_game_state = {}
        self.state_history = []
        self.logger.info("Game state and history reset.")

    def query_state(self, key: str, default: Any = None) -> Any:
        return self.current_game_state.get(key, default)

# Attempt to import MinecraftEnvironmentManager
try:
    from ..testing.minecraft_environment import MinecraftEnvironmentManager
except (ImportError, ValueError):
    logging.warning("Could not import MinecraftEnvironmentManager. Using dummy version.")
    class MinecraftEnvironmentManager:
        def __init__(self, *args, **kwargs): self.logger = logging.getLogger("DummyMinecraftEnvironmentManager")
        def execute_command(self, command: str): self.logger.info(f"Simulating command: {command}"); return {}
        def initialize_environment(self): self.logger.info("Dummy Env Init")
        def start_server(self): self.logger.info("Dummy Env Start")
        def stop_server(self): self.logger.info("Dummy Env Stop")
        def reset_environment(self): self.logger.info("Dummy Env Reset")


class TestScenarioExecutor:
    def __init__(self, environment_manager: MinecraftEnvironmentManager, game_state_tracker: GameStateTracker):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.env_manager = environment_manager
        self.state_tracker = game_state_tracker
        self.logger.info("TestScenarioExecutor initialized.")

    def load_scenario(self, scenario_data: Dict[str, Any]) -> Dict[str, Any]:
        self.logger.info(f"Loading scenario: {scenario_data.get('scenario', 'Unnamed Scenario')}")
        if not all(k in scenario_data for k in ['scenario', 'steps']):
            self.logger.error("Scenario data is missing required keys ('scenario', 'steps').")
            raise ValueError("Invalid scenario format: 'scenario' and 'steps' are required.")
        return scenario_data

    def execute_scenario(self, scenario_data: Dict[str, Any]) -> Dict[str, Any]:
        scenario_name = scenario_data.get('scenario', 'Unnamed Scenario')
        self.logger.info(f"Starting execution of scenario: {scenario_name}")
        self.state_tracker.reset_state()
        results = {"scenario_name": scenario_name, "steps_executed": 0, "steps_succeeded": 0, "steps_failed": 0,
                   "step_results": [], "execution_time_ms": 0, "final_status": "PENDING"}
        start_time = time.time()
        for i, step in enumerate(scenario_data.get('steps', [])):
            self.logger.info(f"Executing step {i+1}/{len(scenario_data['steps'])}: {step.get('action', 'No action')}")
            results["steps_executed"] += 1
            step_result = self._execute_step(step)
            results["step_results"].append(step_result)
            if step_result["status"] == "SUCCESS": results["steps_succeeded"] += 1
            else:
                results["steps_failed"] += 1
                if scenario_data.get("fail_fast", False):
                    self.logger.warning(f"Step failed in fail_fast mode. Stopping scenario: {scenario_name}"); break
        end_time = time.time()
        results["execution_time_ms"] = int((end_time - start_time) * 1000)
        if results["steps_failed"] == 0 and results["steps_executed"] > 0: results["final_status"] = "SUCCESS"
        elif results["steps_executed"] == 0: results["final_status"] = "NO_STEPS"
        else: results["final_status"] = "FAILED"
        self.logger.info(f"Finished execution of scenario: {scenario_name}. Status: {results['final_status']}")
        return results

    def _execute_step(self, step: Dict[str, Any]) -> Dict[str, Any]:
        action = step.get("action")
        step_status = "PENDING"; step_message = ""; step_start_time = time.time()
        try:
            if action == "place_block":
                pos_str = str(step.get('position', '[0,0,0]')); block_type = step.get('block_type', 'unknown_block')
                self.logger.info(f"Simulating action: {action} at {pos_str} with {block_type}")
                self.state_tracker.update_state({f"block_at_{pos_str}": block_type})
                step_status = "SUCCESS"; step_message = f"Block '{block_type}' placed at {pos_str} (simulated)."
            elif action == "spawn_entity":
                entity_type = step.get('type', 'unknown_entity'); entity_id = f"entity_{str(time.time())}"
                self.logger.info(f"Simulating action: {action} of type {entity_type} at {step.get('position', 'default_pos')}")
                self.state_tracker.update_state({"last_spawned_entity": {"type": entity_type, "id": entity_id}})
                step_status = "SUCCESS"; step_message = f"Entity '{entity_type}' (id: {entity_id}) spawned (simulated)."
            elif action == "right_click":
                target = step.get('target', 'unknown_target')
                self.logger.info(f"Simulating action: {action} on target {target}")
                self.state_tracker.update_state({"last_interaction": {"type": "right_click", "target": target, "timestamp": time.time()}})
                if target == "custom_block_A": self.state_tracker.update_state({"gui_opened_for_custom_block_A": "main_menu_mock_value"})
                step_status = "SUCCESS"; step_message = f"Right-click on '{target}' (simulated)."
            elif action == "verify_state":
                key_to_verify = step.get("key"); expected_value = step.get("expected")
                actual_value = self.state_tracker.query_state(key_to_verify)
                self.logger.info(f"Verifying state: '{key_to_verify}', Expected: '{expected_value}', Actual: '{actual_value}'")
                if str(actual_value) == str(expected_value):
                    step_status = "SUCCESS"; step_message = f"State '{key_to_verify}' matches."
                else:
                    step_status = "FAILURE"; step_message = f"State '{key_to_verify}' mismatch. Expected: '{expected_value}', Actual: '{actual_value}'."
            elif action == "player_approach":
                target = step.get('target', 'unknown_target'); distance = step.get('distance', 0)
                self.logger.info(f"Simulating action: {action} to target '{target}' at distance {distance}")
                self.state_tracker.update_state({"player_proximity_event": {"target": target, "distance": distance, "timestamp": time.time()}})
                step_status = "SUCCESS"; step_message = f"Player approached '{target}' (simulated)."
            elif action == "verify_behavior":
                expected_behavior_id = step.get('expected_behavior_id', 'unknown_behavior')
                self.logger.info(f"Simulating behavior verification: {expected_behavior_id}")
                if self.state_tracker.query_state("last_interaction") or self.state_tracker.query_state("player_proximity_event"):
                    step_status = "SUCCESS"; step_message = f"Behavior '{expected_behavior_id}' verified (simulated)."
                else:
                    step_status = "FAILURE"; step_message = f"No recent event for behavior '{expected_behavior_id}' (simulated)."
            else: self.logger.warning(f"Unknown action: {action}"); step_status = "SKIPPED"; step_message = f"Action '{action}' not implemented."
        except Exception as e: self.logger.error(f"Error in step '{action}': {e}", exc_info=True); step_status = "ERROR"; step_message = str(e)
        exec_time_ms = int((time.time() - step_start_time) * 1000)
        return {"action": action, "details": step, "status": step_status, "message": step_message, "execution_time_ms": exec_time_ms}


class BehavioralAnalyzer:
    def __init__(self, game_state_tracker: GameStateTracker):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.state_tracker = game_state_tracker
        self.logger.info("BehavioralAnalyzer initialized.")

    def compare_behaviors(self,
                          expected_behavior: Dict[str, Any],
                          scenario_name: str = "UnknownScenario",
                          actual_behavior_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        self.logger.info(f"Starting behavior analysis for scenario: {scenario_name}, expected: {expected_behavior}")
        analysis_result = {"match": False, "details": "Analysis not performed.",
                           "expected": expected_behavior, "actual": None}
        behavior_type = expected_behavior.get("type")

        if behavior_type == "state_change":
            key = expected_behavior.get("key"); expected_value = expected_behavior.get("expected_value")
            if key is None or expected_value is None:
                analysis_result["details"] = "Invalid 'state_change': 'key' and 'expected_value' required."
                return analysis_result
            actual_value = self.state_tracker.query_state(key) if actual_behavior_context is None \
                else actual_behavior_context.get(key)
            analysis_result["actual"] = {key: actual_value}
            if str(actual_value) == str(expected_value):
                analysis_result["match"] = True; analysis_result["details"] = f"State '{key}' matches."
            else:
                analysis_result["details"] = f"State '{key}' mismatch. Expected: '{expected_value}', Actual: '{actual_value}'."

        elif behavior_type == "event_sequence":
            expected_events = expected_behavior.get("events", [])
            analysis_result["actual"] = {"summary": "Event sequence analysis is placeholder."}
            if not expected_events: analysis_result["details"] = "No events for 'event_sequence'."; return analysis_result
            if self.state_tracker.get_state_history(): # Placeholder logic
                analysis_result["match"] = True; analysis_result["details"] = f"Simulated match for events: {expected_events}."
            else: analysis_result["details"] = f"Simulated mismatch for events: {expected_events} (no history)."

        elif behavior_type == "action_mapping":
            java_action_id = expected_behavior.get("java_action_id")
            bedrock_outcome = expected_behavior.get("bedrock_equivalent_outcome")
            if not java_action_id or not bedrock_outcome:
                analysis_result["details"] = "Invalid 'action_mapping': requires 'java_action_id' and 'bedrock_equivalent_outcome'."
                return analysis_result
            key_to_check = bedrock_outcome.get("key"); expected_val = bedrock_outcome.get("expected_value")
            actual_val = self.state_tracker.query_state(key_to_check) if actual_behavior_context is None \
                else actual_behavior_context.get(key_to_check)
            analysis_result["actual"] = {key_to_check: actual_val}
            if str(actual_val) == str(expected_val):
                analysis_result["match"] = True; analysis_result["details"] = f"Action map '{java_action_id}': Bedrock outcome '{key_to_check}={expected_val}' matches."
            else: analysis_result["details"] = f"Action map '{java_action_id}': Bedrock outcome '{key_to_check}' mismatch. Expected: '{expected_val}', Actual: '{actual_val}'."

        else: analysis_result["details"] = f"Unknown behavior type: '{behavior_type}'."
        self.logger.debug(f"Analysis for {scenario_name}: {analysis_result['details']}")
        return analysis_result

    def analyze_interaction_patterns(self, interaction_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        self.logger.info("Analyzing interaction patterns (placeholder).")
        if not interaction_history: return {"pattern": "none", "details": "No history."}
        counts = {}
        for interaction in interaction_history:
            action_type = interaction.get("type", "unknown")
            counts[action_type] = counts.get(action_type, 0) + 1
        return {"pattern": "summary", "summary": counts, "details": "Basic interaction summary."}


class TestResultProcessor:
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.processed_results: List[Dict[str, Any]] = []
        self.logger.info("TestResultProcessor initialized.")

    def process_scenario_result(self, scenario_execution_output: Dict[str, Any],
                                behavioral_analysis_results: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        self.logger.info(f"Processing results for scenario: {scenario_execution_output.get('scenario_name', 'Unknown Scenario')}")
        if behavioral_analysis_results is None: behavioral_analysis_results = []

        processed_scenario_result = {
            "scenario_name": scenario_execution_output.get("scenario_name", "Unknown Scenario"),
            "overall_status": scenario_execution_output.get("final_status", "UNKNOWN"),
            "execution_time_ms": scenario_execution_output.get("execution_time_ms", 0),
            "steps_total": scenario_execution_output.get("steps_executed", 0),
            "steps_succeeded": scenario_execution_output.get("steps_succeeded", 0),
            "steps_failed": scenario_execution_output.get("steps_failed", 0),
            "step_details": scenario_execution_output.get("step_results", []),
            "behavioral_analyses": behavioral_analysis_results,
            "issues_detected": []
        }
        num_behavior_mismatches = 0
        for analysis in behavioral_analysis_results:
            if not analysis.get("match", True):
                num_behavior_mismatches +=1
                issue_detail = {"type": "behavior_mismatch", "scenario": processed_scenario_result["scenario_name"],
                                "details": analysis.get("details", "No details."), "expected": analysis.get("expected"),
                                "actual": analysis.get("actual")}
                processed_scenario_result["issues_detected"].append(issue_detail)

        if num_behavior_mismatches > 0 and processed_scenario_result["overall_status"] == "SUCCESS":
            processed_scenario_result["overall_status"] = "FAILED_BEHAVIOR"
            self.logger.warning(f"Scenario '{processed_scenario_result['scenario_name']}' FAILED_BEHAVIOR.")

        self.processed_results.append(processed_scenario_result)
        self.logger.info(f"Finished processing for {processed_scenario_result['scenario_name']}. Status: {processed_scenario_result['overall_status']}")
        return processed_scenario_result

    def process_batch_results(self, multiple_scenario_outputs: List[Dict[str, Any]],
                              multiple_behavioral_analyses: List[List[Dict[str, Any]]] = None) -> List[Dict[str, Any]]:
        self.logger.info(f"Processing batch of {len(multiple_scenario_outputs)} results.")
        batch_processed = []
        if multiple_behavioral_analyses is None: multiple_behavioral_analyses = [[] for _ in multiple_scenario_outputs]
        if len(multiple_scenario_outputs) != len(multiple_behavioral_analyses):
            self.logger.warning("Mismatch in scenario outputs and behavioral analyses lengths.")
            multiple_behavioral_analyses = (multiple_behavioral_analyses + [[] for _ in range(len(multiple_scenario_outputs))])[:len(multiple_scenario_outputs)]

        for i, scenario_output in enumerate(multiple_scenario_outputs):
            analyses_for_scenario = multiple_behavioral_analyses[i] if i < len(multiple_behavioral_analyses) else []
            processed = self.process_scenario_result(scenario_output, analyses_for_scenario)
            batch_processed.append(processed)
        return batch_processed

    def get_summary(self) -> Dict[str, Any]:
        if not self.processed_results: return {"message": "No results processed."}
        total = len(self.processed_results)
        passed = sum(1 for r in self.processed_results if r["overall_status"] in ["SUCCESS", "PASSED"])
        failed = total - passed
        total_time = sum(r.get("execution_time_ms", 0) for r in self.processed_results)
        all_issues = [issue for r in self.processed_results for issue in r.get("issues_detected", [])]
        return {"total_scenarios_processed": total, "scenarios_passed": passed, "scenarios_failed_or_with_issues": failed, # Corrected key name
                "total_execution_time_ms": total_time, "total_issues_detected": len(all_issues), "issues_summary": all_issues} # Corrected key name

    def clear_results(self):
        self.logger.info("Clearing processed results.")
        self.processed_results = []


class BehavioralReportGenerator:
    def __init__(self, report_directory: str = "./reports/behavioral"):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.report_directory = report_directory
        self.logger.info(f"BehavioralReportGenerator initialized. Reports to: {self.report_directory} (if paths written)")

    def generate_report(self,
                        processed_test_summary: Dict[str, Any],
                        report_format: str = "json",
                        filename_prefix: str = "behavioral_report") -> Union[str, Dict[str, Any]]:
        self.logger.info(f"Generating behavioral report in '{report_format}' format.")
        report_content: Union[str, Dict[str, Any]] = {}
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

        report_data_to_embed = processed_test_summary.copy()
        report_data_to_embed["report_generated_at"] = timestamp
        report_data_to_embed["report_type"] = "Behavioral Test Summary"

        if report_format == "json":
            report_content = report_data_to_embed
            return report_content

        elif report_format == "text":
            lines = []
            lines.append(f"--- {report_data_to_embed['report_type']} ---")
            lines.append(f"Generated At: {report_data_to_embed['report_generated_at']}")
            lines.append("-" * 30)
            lines.append(f"Total Scenarios Processed: {report_data_to_embed.get('total_scenarios_processed', 'N/A')}")
            lines.append(f"Scenarios Passed: {report_data_to_embed.get('scenarios_passed', 'N/A')}")
            lines.append(f"Scenarios Failed/With Issues: {report_data_to_embed.get('scenarios_failed_or_with_issues', 'N/A')}")
            lines.append(f"Total Execution Time (ms): {report_data_to_embed.get('total_execution_time_ms', 'N/A')}")
            lines.append(f"Total Issues Detected: {report_data_to_embed.get('total_issues_detected', 'N/A')}")
            lines.append("-" * 30)

            if "issues_summary" in report_data_to_embed and report_data_to_embed["issues_summary"]:
                lines.append("\nDetected Issues Summary:")
                for i, issue in enumerate(report_data_to_embed["issues_summary"]):
                    lines.append(f"  Issue {i+1}:")
                    lines.append(f"    Type: {issue.get('type', 'N/A')}")
                    lines.append(f"    Scenario: {issue.get('scenario', 'N/A')}")
                    lines.append(f"    Details: {issue.get('details', 'N/A')}")
                    if issue.get("expected") is not None: lines.append(f"    Expected: {json.dumps(issue.get('expected'))}")
                    if issue.get("actual") is not None: lines.append(f"    Actual: {json.dumps(issue.get('actual'))}")
            else: lines.append("\nNo specific issues detected or summarized.")

            if "processed_results_list" in report_data_to_embed:
                lines.append("\n--- Individual Scenario Details ---")
                for scenario_res in report_data_to_embed["processed_results_list"]:
                    lines.append(f"\nScenario: {scenario_res.get('scenario_name', 'N/A')}")
                    lines.append(f"  Status: {scenario_res.get('overall_status', 'N/A')}")
                    lines.append(f"  Execution Time (ms): {scenario_res.get('execution_time_ms', 'N/A')}")
                    lines.append(f"  Steps: {scenario_res.get('steps_succeeded', 'N/A')}/{scenario_res.get('steps_total', 'N/A')} succeeded")
                    if scenario_res.get("behavioral_analyses"):
                        lines.append(f"  Behavioral Checks: {len(scenario_res.get('behavioral_analyses', []))}")
                        for ba in scenario_res.get('behavioral_analyses', []):
                            match_s = "MATCH" if ba.get('match') else "MISMATCH"
                            lines.append(f"    - Check: {ba.get('details', 'No details')} (Status: {match_s})")
            report_content = "\n".join(lines)
            return report_content

        elif report_format == "html":
            self.logger.warning("HTML report format is basic. Returning basic HTML string.")
            html_lines = ["<html><head><title>Behavioral Test Report</title></head><body>"]
            html_lines.append(f"<h1>{report_data_to_embed['report_type']}</h1>")
            html_lines.append(f"<p>Generated At: {report_data_to_embed['report_generated_at']}</p><hr>")
            html_lines.append("<h2>Summary</h2><ul>")
            for k, v in report_data_to_embed.items():
                if k not in ["issues_summary", "processed_results_list"]:
                     html_lines.append(f"<li><strong>{k.replace('_', ' ').title()}:</strong> {v}</li>")
            html_lines.append("</ul>")
            if "issues_summary" in report_data_to_embed and report_data_to_embed["issues_summary"]:
                html_lines.append("<h2>Detected Issues</h2><table border='1'><tr><th>Type</th><th>Scenario</th><th>Details</th><th>Expected</th><th>Actual</th></tr>")
                for issue in report_data_to_embed["issues_summary"]:
                    html_lines.append(f"<tr><td>{issue.get('type','')}</td><td>{issue.get('scenario','')}</td><td>{issue.get('details','')}</td><td><pre>{json.dumps(issue.get('expected'), indent=2)}</pre></td><td><pre>{json.dumps(issue.get('actual'), indent=2)}</pre></td></tr>")
                html_lines.append("</table>")
            html_lines.append("</body></html>")
            report_content = "\n".join(html_lines)
            return report_content
        else:
            self.logger.error(f"Unsupported report format: {report_format}")
            raise ValueError(f"Unsupported format: {report_format}. Use json, text, or html.")


class BehavioralTestingFramework:
    """
    Main behavioral testing framework that orchestrates all testing components.
    
    This class provides the primary interface for conducting behavioral tests,
    managing test execution workflows, and generating comprehensive reports.
    """
    
    def __init__(self, test_environment_config: Optional[Dict[str, Any]] = None):
        """
        Initialize the behavioral testing framework.
        
        Args:
            test_environment_config: Configuration for the testing environment
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = test_environment_config or {}
        
        # Initialize core components
        self.environment_manager = MinecraftEnvironmentManager(
            server_ip=self.config.get('server_ip', 'localhost'),
            server_port=self.config.get('server_port', 19132)
        )
        self.game_state_tracker = GameStateTracker()
        self.scenario_executor = TestScenarioExecutor(
            self.environment_manager, 
            self.game_state_tracker
        )
        self.behavior_analyzer = BehavioralAnalyzer(self.game_state_tracker)
        self.result_processor = TestResultProcessor()
        self.report_generator = BehavioralReportGenerator(
            report_directory=self.config.get('report_directory', './reports/behavioral')
        )
        
        self.logger.info("BehavioralTestingFramework initialized successfully.")
    
    def run_behavioral_test(self, 
                           test_scenarios: List[Dict[str, Any]], 
                           expected_behaviors: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Execute a complete behavioral test suite.
        
        Args:
            test_scenarios: List of test scenarios to execute
            expected_behaviors: List of expected behavior specifications
            
        Returns:
            Comprehensive test results and analysis
        """
        self.logger.info(f"Starting behavioral test execution with {len(test_scenarios)} scenarios.")
        
        try:
            # Initialize test environment
            self.environment_manager.initialize_environment()
            self.environment_manager.start_server()
            
            scenario_results = []
            behavioral_analyses = []
            
            # Execute each scenario
            for i, scenario in enumerate(test_scenarios):
                self.logger.info(f"Executing scenario {i+1}/{len(test_scenarios)}: {scenario.get('scenario', 'Unnamed')}")
                
                # Load and execute scenario
                loaded_scenario = self.scenario_executor.load_scenario(scenario)
                execution_result = self.scenario_executor.execute_scenario(loaded_scenario)
                scenario_results.append(execution_result)
                
                # Perform behavioral analysis if expected behaviors provided
                if expected_behaviors and i < len(expected_behaviors):
                    analysis = self.behavior_analyzer.compare_behaviors(
                        expected_behaviors[i],
                        scenario_name=scenario.get('scenario', f'Scenario_{i+1}')
                    )
                    behavioral_analyses.append([analysis])
                else:
                    behavioral_analyses.append([])
            
            # Process results
            processed_results = self.result_processor.process_batch_results(
                scenario_results, 
                behavioral_analyses
            )
            
            # Generate summary and report
            test_summary = self.result_processor.get_summary()
            test_summary['processed_results_list'] = processed_results
            
            # Generate final report
            final_report = self.report_generator.generate_report(
                test_summary,
                report_format=self.config.get('report_format', 'json')
            )
            
            self.logger.info("Behavioral test execution completed successfully.")
            
            return {
                'test_summary': test_summary,
                'processed_results': processed_results,
                'final_report': final_report,
                'status': 'COMPLETED'
            }
            
        except Exception as e:
            self.logger.error(f"Error during behavioral test execution: {e}", exc_info=True)
            return {
                'status': 'FAILED',
                'error': str(e),
                'test_summary': self.result_processor.get_summary()
            }
        finally:
            # Cleanup
            try:
                self.environment_manager.stop_server()
            except Exception as cleanup_error:
                self.logger.warning(f"Error during cleanup: {cleanup_error}")
    
    def validate_mod_conversion(self, 
                               original_mod_path: str, 
                               converted_addon_path: str) -> Dict[str, Any]:
        """
        Validate that a converted add-on maintains original mod behavior.
        
        Args:
            original_mod_path: Path to original Java mod
            converted_addon_path: Path to converted Bedrock add-on
            
        Returns:
            Validation results comparing original vs converted behavior
        """
        self.logger.info(f"Starting mod conversion validation: {original_mod_path} -> {converted_addon_path}")
        
        # This would be implemented to generate test scenarios based on mod analysis
        # For now, return a placeholder structure
        return {
            'validation_status': 'PENDING',
            'message': 'Mod conversion validation is not yet fully implemented',
            'original_mod': original_mod_path,
            'converted_addon': converted_addon_path
        }


if __name__ == '__main__':
    logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    mock_scenario1 = {"scenario_name": "Alpha", "overall_status": "SUCCESS", "execution_time_ms": 100,
                      "steps_total": 2, "steps_succeeded": 2, "steps_failed": 0, "behavioral_analyses":
                      [{"match": True, "details": "OK"}], "issues_detected": []}
    mock_scenario2 = {"scenario_name": "Bravo", "overall_status": "FAILED_BEHAVIOR", "execution_time_ms": 200,
                      "steps_total": 3, "steps_succeeded": 3, "steps_failed": 0, "behavioral_analyses":
                      [{"match": False, "details": "Mismatch", "expected": {"s": "X"}, "actual": {"s": "Y"}}],
                      "issues_detected": [{"type": "bm", "scenario": "Bravo", "details": "Mismatch", "expected": {"s": "X"}, "actual": {"s": "Y"}}]}

    mock_summary_from_processor = {
        "total_scenarios_processed": 2, "scenarios_passed": 1, "scenarios_failed_or_with_issues": 1,
        "total_execution_time_ms": 300, "total_issues_detected": 1,
        "issues_summary": [{"type": "bm", "scenario": "Bravo", "details": "Mismatch", "expected": {"s": "X"}, "actual": {"s": "Y"}}],
        "processed_results_list": [mock_scenario1, mock_scenario2]
    }

    generator = BehavioralReportGenerator(report_directory="./temp_reports")

    print("\n--- JSON Report ---")
    json_rep = generator.generate_report(mock_summary_from_processor, report_format="json")
    print(json.dumps(json_rep, indent=2))

    print("\n--- Text Report ---")
    text_rep = generator.generate_report(mock_summary_from_processor, report_format="text")
    print(text_rep)

    print("\n--- HTML Report ---")
    html_rep = generator.generate_report(mock_summary_from_processor, report_format="html")
    print(html_rep) # Basic print, view in browser for actual render.
    # with open("temp_behavioral_report.html", "w") as f: f.write(html_rep) # Example save

    try:
        generator.generate_report(mock_summary_from_processor, report_format="pdf")
    except ValueError as e:
        print(f"\nError for unsupported format: {e}")
